import React, { useState } from 'react';
import { FaComments, FaPlay, FaPause, FaMicrophone, FaStop, FaStar, FaLightbulb } from 'react-icons/fa';

const InterviewBot = () => {
  const [selectedRole, setSelectedRole] = useState('');
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [questionIndex, setQuestionIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [feedback, setFeedback] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [sessionStarted, setSessionStarted] = useState(false);

  const jobRoles = [
    'Software Engineer',
    'Product Manager',
    'Data Scientist',
    'UX Designer',
    'Marketing Manager',
    'Sales Representative',
    'Business Analyst',
    'DevOps Engineer'
  ];

  const questionSets = {
    'Software Engineer': [
      {
        question: "Tell me about yourself and your experience in software development.",
        type: "behavioral",
        tips: "Focus on relevant experience, key projects, and technical skills"
      },
      {
        question: "How would you approach debugging a complex issue in production?",
        type: "technical",
        tips: "Mention systematic approach, logging, monitoring, and collaboration"
      },
      {
        question: "Describe a challenging project you worked on and how you overcame obstacles.",
        type: "behavioral",
        tips: "Use STAR method: Situation, Task, Action, Result"
      },
      {
        question: "What's the difference between REST and GraphQL APIs?",
        type: "technical",
        tips: "Compare data fetching, flexibility, and use cases"
      },
      {
        question: "How do you stay updated with new technologies and best practices?",
        type: "behavioral",
        tips: "Mention learning resources, communities, and continuous improvement"
      }
    ],
    'Product Manager': [
      {
        question: "How do you prioritize features when you have limited resources?",
        type: "strategic",
        tips: "Discuss frameworks like RICE, user impact, and business value"
      },
      {
        question: "Tell me about a time you had to make a difficult product decision.",
        type: "behavioral",
        tips: "Show data-driven decision making and stakeholder management"
      },
      {
        question: "How would you handle conflicting requirements from different stakeholders?",
        type: "behavioral",
        tips: "Emphasize communication, compromise, and alignment with goals"
      }
    ]
  };

  const startInterview = () => {
    if (!selectedRole) return;
    setSessionStarted(true);
    setQuestionIndex(0);
    setCurrentQuestion(questionSets[selectedRole][0]);
    setUserAnswer('');
    setFeedback(null);
  };

  const nextQuestion = () => {
    const questions = questionSets[selectedRole];
    if (questionIndex < questions.length - 1) {
      setQuestionIndex(questionIndex + 1);
      setCurrentQuestion(questions[questionIndex + 1]);
      setUserAnswer('');
      setFeedback(null);
    } else {
      // Interview complete
      setSessionStarted(false);
      setCurrentQuestion(null);
    }
  };

  const submitAnswer = () => {
    // Simulate GPT feedback
    const mockFeedback = {
      score: Math.floor(Math.random() * 30) + 70, // 70-100
      strengths: [
        "Clear and structured response",
        "Good use of specific examples",
        "Demonstrated technical knowledge"
      ],
      improvements: [
        "Could provide more quantifiable results",
        "Consider mentioning team collaboration",
        "Add more details about the impact"
      ],
      suggestion: "Great answer! Try to include more specific metrics to make your response even stronger."
    };
    setFeedback(mockFeedback);
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    // In a real app, this would start/stop audio recording
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <FaComments className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Interview Q&A Bot</h1>
            <p className="text-gray-600">Practice interview questions with AI-powered feedback</p>
          </div>
        </div>
      </div>

      {!sessionStarted ? (
        /* Setup Section */
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Start Your Interview Practice</h2>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select Job Role
              </label>
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="w-full max-w-md px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 text-center"
              >
                <option value="">Choose a role...</option>
                {jobRoles.map((role) => (
                  <option key={role} value={role}>{role}</option>
                ))}
              </select>
            </div>

            <button
              onClick={startInterview}
              disabled={!selectedRole}
              className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-8 py-3 rounded-md font-medium text-lg flex items-center space-x-2 mx-auto"
            >
              <FaPlay className="h-5 w-5" />
              <span>Start Interview</span>
            </button>

            <div className="mt-8 text-left max-w-md mx-auto">
              <h3 className="font-semibold text-gray-900 mb-3">What to expect:</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                  <span>5-7 role-specific questions</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                  <span>AI-powered feedback on each answer</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                  <span>Tips for improvement</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                  <span>Voice recording option</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      ) : (
        /* Interview Session */
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Question Panel */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Question {questionIndex + 1} of {questionSets[selectedRole].length}
                  </h2>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    currentQuestion?.type === 'technical' ? 'bg-blue-100 text-blue-700' :
                    currentQuestion?.type === 'behavioral' ? 'bg-green-100 text-green-700' :
                    'bg-purple-100 text-purple-700'
                  }`}>
                    {currentQuestion?.type}
                  </span>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <p className="text-lg text-gray-900">{currentQuestion?.question}</p>
                </div>

                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Your Answer
                    </label>
                    <button
                      onClick={toggleRecording}
                      className={`flex items-center space-x-2 px-3 py-1 rounded-md text-sm font-medium ${
                        isRecording 
                          ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {isRecording ? <FaStop className="h-4 w-4" /> : <FaMicrophone className="h-4 w-4" />}
                      <span>{isRecording ? 'Stop Recording' : 'Voice Answer'}</span>
                    </button>
                  </div>
                  <textarea
                    value={userAnswer}
                    onChange={(e) => setUserAnswer(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Type your answer here..."
                  />
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={submitAnswer}
                    disabled={!userAnswer.trim()}
                    className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md font-medium"
                  >
                    Get Feedback
                  </button>
                  {feedback && (
                    <button
                      onClick={nextQuestion}
                      className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium"
                    >
                      Next Question
                    </button>
                  )}
                </div>
              </div>

              {/* Feedback Panel */}
              {feedback && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <FaStar className="mr-2 text-yellow-500" />
                    AI Feedback
                  </h3>
                  
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Overall Score</span>
                      <span className="text-2xl font-bold text-primary-600">{feedback.score}/100</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-primary-600 h-3 rounded-full"
                        style={{ width: `${feedback.score}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-green-600 mb-2">Strengths</h4>
                      <ul className="space-y-1">
                        {feedback.strengths.map((strength, index) => (
                          <li key={index} className="text-sm text-gray-700 flex items-start">
                            <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-yellow-600 mb-2">Areas for Improvement</h4>
                      <ul className="space-y-1">
                        {feedback.improvements.map((improvement, index) => (
                          <li key={index} className="text-sm text-gray-700 flex items-start">
                            <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            {improvement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                    <p className="text-blue-800 text-sm">{feedback.suggestion}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Tips Sidebar */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FaLightbulb className="mr-2 text-yellow-500" />
                  Tips for This Question
                </h3>
                <p className="text-sm text-gray-700">{currentQuestion?.tips}</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Progress</h3>
                <div className="space-y-2">
                  {questionSets[selectedRole].map((_, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        index < questionIndex ? 'bg-green-500' :
                        index === questionIndex ? 'bg-primary-500' :
                        'bg-gray-300'
                      }`}></div>
                      <span className={`text-sm ${
                        index === questionIndex ? 'font-medium text-gray-900' : 'text-gray-600'
                      }`}>
                        Question {index + 1}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InterviewBot;
