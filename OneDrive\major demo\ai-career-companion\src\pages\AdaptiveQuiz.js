import React, { useState } from 'react';
import { FaQuestionCircle, FaPlay, FaCheck, FaTimes, FaStar } from 'react-icons/fa';

const AdaptiveQuiz = () => {
  const [quizStarted, setQuizStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState('');
  const [score, setScore] = useState(0);
  const [quizComplete, setQuizComplete] = useState(false);

  const questions = [
    {
      question: "What is the time complexity of binary search?",
      options: ["O(n)", "O(log n)", "O(n²)", "O(1)"],
      correct: 1,
      difficulty: "Medium"
    },
    {
      question: "Which of the following is NOT a JavaScript data type?",
      options: ["String", "Boolean", "Float", "Undefined"],
      correct: 2,
      difficulty: "Easy"
    },
    {
      question: "What does REST stand for?",
      options: ["Representational State Transfer", "Remote State Transfer", "Relational State Transfer", "Resource State Transfer"],
      correct: 0,
      difficulty: "Medium"
    }
  ];

  const startQuiz = () => {
    setQuizStarted(true);
    setCurrentQuestion(0);
    setScore(0);
    setQuizComplete(false);
  };

  const handleAnswerSelect = (answerIndex) => {
    setSelectedAnswer(answerIndex);
  };

  const nextQuestion = () => {
    if (selectedAnswer === questions[currentQuestion].correct) {
      setScore(score + 1);
    }
    
    if (currentQuestion + 1 < questions.length) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer('');
    } else {
      setQuizComplete(true);
    }
  };

  const resetQuiz = () => {
    setQuizStarted(false);
    setCurrentQuestion(0);
    setSelectedAnswer('');
    setScore(0);
    setQuizComplete(false);
  };

  if (!quizStarted) {
    return (
      <div className="space-y-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FaQuestionCircle className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Adaptive Quiz System</h1>
              <p className="text-gray-600">Test your knowledge with personalized questions</p>
            </div>
          </div>
        </div>

        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Ready to Start?</h2>
          <p className="text-gray-600 mb-6">This adaptive quiz will adjust difficulty based on your performance</p>
          <button
            onClick={startQuiz}
            className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-md font-medium text-lg flex items-center space-x-2 mx-auto"
          >
            <FaPlay className="h-5 w-5" />
            <span>Start Quiz</span>
          </button>
        </div>
      </div>
    );
  }

  if (quizComplete) {
    return (
      <div className="space-y-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <FaStar className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Quiz Complete!</h1>
              <p className="text-gray-600">Great job on completing the quiz</p>
            </div>
          </div>
        </div>

        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-3xl font-bold text-primary-600 mb-4">Your Score: {score}/{questions.length}</h2>
          <p className="text-gray-600 mb-6">
            {score === questions.length ? 'Perfect score! Excellent work!' :
             score >= questions.length * 0.7 ? 'Great job! You have a solid understanding.' :
             'Good effort! Consider reviewing the topics covered.'}
          </p>
          <button
            onClick={resetQuiz}
            className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md font-medium"
          >
            Take Another Quiz
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FaQuestionCircle className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Question {currentQuestion + 1} of {questions.length}</h1>
              <p className="text-gray-600">Difficulty: {questions[currentQuestion].difficulty}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">Current Score</p>
            <p className="text-2xl font-bold text-primary-600">{score}/{currentQuestion}</p>
          </div>
        </div>
      </div>

      <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md p-8">
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">{questions[currentQuestion].question}</h2>
          <div className="space-y-3">
            {questions[currentQuestion].options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(index)}
                className={`w-full text-left p-4 rounded-lg border-2 transition-colors ${
                  selectedAnswer === index
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                    selectedAnswer === index
                      ? 'border-primary-500 bg-primary-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedAnswer === index && <FaCheck className="h-3 w-3 text-white" />}
                  </div>
                  <span className="text-gray-900">{option}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Progress: {Math.round(((currentQuestion + 1) / questions.length) * 100)}%
          </div>
          <button
            onClick={nextQuestion}
            disabled={selectedAnswer === ''}
            className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md font-medium"
          >
            {currentQuestion + 1 === questions.length ? 'Finish Quiz' : 'Next Question'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdaptiveQuiz;
