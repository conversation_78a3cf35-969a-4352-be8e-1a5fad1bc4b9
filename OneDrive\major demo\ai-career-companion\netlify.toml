[build]
  # Directory to change to before starting a build
  base = "."
  
  # Directory that contains the deploy-ready HTML files and assets generated by the build
  publish = "build"
  
  # Default build command
  command = "npm run build"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"
  
  # NPM version
  NPM_VERSION = "9"

# Redirect rules for React Router (SPA)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Error pages
[[redirects]]
  from = "/404"
  to = "/404.html"
  status = 404
