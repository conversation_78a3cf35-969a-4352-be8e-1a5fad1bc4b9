import React, { useState } from 'react';
import { FaRoute, FaUser, FaHeart, FaCogs, FaChartLine, FaStar, FaArrowRight } from 'react-icons/fa';

const CareerPathRecommender = () => {
  const [formData, setFormData] = useState({
    currentRole: '',
    experience: '',
    skills: '',
    interests: '',
    goals: '',
    workStyle: ''
  });
  const [recommendations, setRecommendations] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsAnalyzing(true);

    // Simulate API call
    setTimeout(() => {
      const mockRecommendations = [
        {
          title: 'Senior Software Engineer',
          match: 92,
          description: 'Lead technical projects and mentor junior developers',
          requirements: ['5+ years experience', 'Advanced programming skills', 'Leadership abilities'],
          salaryRange: '$120k - $180k',
          growthPath: ['Tech Lead', 'Engineering Manager', 'VP of Engineering'],
          companies: ['Google', 'Microsoft', 'Amazon', 'Meta']
        },
        {
          title: 'Product Manager',
          match: 85,
          description: 'Drive product strategy and work with cross-functional teams',
          requirements: ['Technical background', 'Business acumen', 'Communication skills'],
          salaryRange: '$110k - $160k',
          growthPath: ['Senior PM', 'Director of Product', 'VP of Product'],
          companies: ['Apple', 'Netflix', 'Uber', 'Airbnb']
        },
        {
          title: 'Data Scientist',
          match: 78,
          description: 'Analyze data to drive business insights and decisions',
          requirements: ['Statistics knowledge', 'Python/R skills', 'Machine learning'],
          salaryRange: '$100k - $150k',
          growthPath: ['Senior Data Scientist', 'Lead Data Scientist', 'Chief Data Officer'],
          companies: ['Tesla', 'LinkedIn', 'Spotify', 'Slack']
        }
      ];
      setRecommendations(mockRecommendations);
      setIsAnalyzing(false);
    }, 2000);
  };

  const getMatchColor = (match) => {
    if (match >= 90) return 'text-green-600 bg-green-100';
    if (match >= 80) return 'text-blue-600 bg-blue-100';
    if (match >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <FaRoute className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Career Path Recommender</h1>
            <p className="text-gray-600">Discover personalized career paths based on your skills and interests</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Input Form */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Tell Us About Yourself</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Current Role
                </label>
                <input
                  type="text"
                  name="currentRole"
                  value={formData.currentRole}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="e.g., Software Developer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Years of Experience
                </label>
                <select
                  name="experience"
                  value={formData.experience}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select experience</option>
                  <option value="0-1">0-1 years</option>
                  <option value="2-3">2-3 years</option>
                  <option value="4-6">4-6 years</option>
                  <option value="7-10">7-10 years</option>
                  <option value="10+">10+ years</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Key Skills
                </label>
                <textarea
                  name="skills"
                  value={formData.skills}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="e.g., JavaScript, React, Node.js, Project Management"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Interests & Passions
                </label>
                <textarea
                  name="interests"
                  value={formData.interests}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="e.g., AI/ML, User Experience, Team Leadership"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Career Goals
                </label>
                <select
                  name="goals"
                  value={formData.goals}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select goal</option>
                  <option value="leadership">Leadership & Management</option>
                  <option value="technical">Technical Expertise</option>
                  <option value="entrepreneurship">Entrepreneurship</option>
                  <option value="consulting">Consulting</option>
                  <option value="research">Research & Innovation</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preferred Work Style
                </label>
                <select
                  name="workStyle"
                  value={formData.workStyle}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select style</option>
                  <option value="collaborative">Collaborative</option>
                  <option value="independent">Independent</option>
                  <option value="client-facing">Client-facing</option>
                  <option value="analytical">Analytical</option>
                  <option value="creative">Creative</option>
                </select>
              </div>

              <button
                type="submit"
                disabled={isAnalyzing}
                className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
              >
                {isAnalyzing ? 'Analyzing...' : 'Get Recommendations'}
              </button>
            </form>
          </div>
        </div>

        {/* Recommendations */}
        <div className="lg:col-span-2">
          {recommendations ? (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Recommended Career Paths</h2>
                <p className="text-gray-600 mb-6">
                  Based on your profile, here are the top career paths that match your skills and interests:
                </p>
              </div>

              {recommendations.map((career, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">{career.title}</h3>
                      <p className="text-gray-600 mt-1">{career.description}</p>
                    </div>
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${getMatchColor(career.match)}`}>
                      {career.match}% Match
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <FaCogs className="mr-2 text-blue-600" />
                        Requirements
                      </h4>
                      <ul className="space-y-1">
                        {career.requirements.map((req, idx) => (
                          <li key={idx} className="text-sm text-gray-600 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <FaChartLine className="mr-2 text-green-600" />
                        Growth Path
                      </h4>
                      <div className="flex items-center space-x-2 text-sm">
                        {career.growthPath.map((step, idx) => (
                          <React.Fragment key={idx}>
                            <span className="text-gray-700 bg-gray-100 px-2 py-1 rounded">{step}</span>
                            {idx < career.growthPath.length - 1 && (
                              <FaArrowRight className="h-3 w-3 text-gray-400" />
                            )}
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Salary Range</h4>
                      <p className="text-lg font-bold text-green-600">{career.salaryRange}</p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Top Companies</h4>
                      <div className="flex flex-wrap gap-2">
                        {career.companies.map((company, idx) => (
                          <span key={idx} className="text-sm bg-primary-100 text-primary-700 px-2 py-1 rounded">
                            {company}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <button className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium mr-3">
                      Learn More
                    </button>
                    <button className="border border-primary-600 text-primary-600 hover:bg-primary-50 px-4 py-2 rounded-md text-sm font-medium">
                      Save Path
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-12 text-center">
              <FaRoute className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Ready to Discover Your Path?</h3>
              <p className="text-gray-600">
                Fill out the form on the left to get personalized career recommendations 
                based on your skills, interests, and goals.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CareerPathRecommender;
