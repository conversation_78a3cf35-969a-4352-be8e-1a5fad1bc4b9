import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import Footer from './components/Footer';

// Import all pages
import Home from './pages/Home';
import About from './pages/About';
import Contact from './pages/Contact';
import JobScamDetector from './pages/JobScamDetector';
import ResumeParser from './pages/ResumeParser';
import CareerPathRecommender from './pages/CareerPathRecommender';
import InterviewBot from './pages/InterviewBot';
import OfferLetterVerifier from './pages/OfferLetterVerifier';
import AdaptiveQuiz from './pages/AdaptiveQuiz';
import StudyPlanner from './pages/StudyPlanner';
import DoubtSolvingChatbot from './pages/DoubtSolvingChatbot';
import WeakTopicIdentifier from './pages/WeakTopicIdentifier';
import TopicRecommender from './pages/TopicRecommender';
import UserProfile from './pages/UserProfile';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Navbar />
        <div className="flex flex-1">
          <Sidebar />
          <main className="main-content">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/about" element={<About />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/job-scam-detector" element={<JobScamDetector />} />
              <Route path="/resume-parser" element={<ResumeParser />} />
              <Route path="/career-path-recommender" element={<CareerPathRecommender />} />
              <Route path="/interview-bot" element={<InterviewBot />} />
              <Route path="/offer-letter-verifier" element={<OfferLetterVerifier />} />
              <Route path="/adaptive-quiz" element={<AdaptiveQuiz />} />
              <Route path="/study-planner" element={<StudyPlanner />} />
              <Route path="/doubt-solving-chatbot" element={<DoubtSolvingChatbot />} />
              <Route path="/weak-topic-identifier" element={<WeakTopicIdentifier />} />
              <Route path="/topic-recommender" element={<TopicRecommender />} />
              <Route path="/profile" element={<UserProfile />} />
            </Routes>
          </main>
        </div>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
