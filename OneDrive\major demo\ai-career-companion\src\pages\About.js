import React from 'react';
import { FaRocket, FaEye, FaHeart, FaUsers, FaBrain, FaShieldAlt } from 'react-icons/fa';

const About = () => {
  const features = [
    {
      icon: FaBrain,
      title: 'AI-Powered Intelligence',
      description: 'Advanced machine learning algorithms provide personalized insights and recommendations.'
    },
    {
      icon: FaShieldAlt,
      title: 'Secure & Private',
      description: 'Your data is protected with enterprise-grade security and privacy measures.'
    },
    {
      icon: FaUsers,
      title: 'Community Driven',
      description: 'Built with feedback from career professionals and learning experts worldwide.'
    }
  ];

  const team = [
    {
      name: '<PERSON>',
      role: 'CEO & Founder',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      bio: 'Former HR executive with 15+ years in talent acquisition and career development.'
    },
    {
      name: '<PERSON>',
      role: 'CTO',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      bio: 'AI researcher and software architect specializing in machine learning applications.'
    },
    {
      name: '<PERSON>',
      role: 'Head of Product',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      bio: 'Product strategist focused on creating user-centric learning and career tools.'
    },
    {
      name: 'David Kim',
      role: 'Lead Developer',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      bio: 'Full-stack developer with expertise in React, Node.js, and AI integration.'
    }
  ];

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">About AI Career Companion</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          We're on a mission to democratize career development and learning through 
          the power of artificial intelligence, making professional growth accessible to everyone.
        </p>
      </div>

      {/* Mission, Vision, Values */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaRocket className="h-8 w-8 text-primary-600" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">Our Mission</h3>
          <p className="text-gray-600">
            To empower individuals with AI-driven tools that accelerate career growth, 
            enhance learning outcomes, and provide personalized guidance for professional success.
          </p>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaEye className="h-8 w-8 text-green-600" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">Our Vision</h3>
          <p className="text-gray-600">
            A world where everyone has access to intelligent career guidance and 
            personalized learning experiences, regardless of their background or location.
          </p>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaHeart className="h-8 w-8 text-red-600" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">Our Values</h3>
          <p className="text-gray-600">
            Innovation, accessibility, privacy, and user-centricity guide everything we do. 
            We believe in ethical AI that serves humanity's best interests.
          </p>
        </div>
      </div>

      {/* Features */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Why Choose Us?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                  <Icon className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            );
          })}
        </div>
      </div>

      {/* Team Section */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">Meet Our Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {team.map((member, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
              <img
                src={member.image}
                alt={member.name}
                className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
              />
              <h3 className="text-lg font-semibold text-gray-900 mb-1">{member.name}</h3>
              <p className="text-primary-600 font-medium mb-3">{member.role}</p>
              <p className="text-gray-600 text-sm">{member.bio}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Stats */}
      <div className="bg-primary-600 rounded-lg text-white p-8">
        <h2 className="text-3xl font-bold text-center mb-8">Our Impact</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-4xl font-bold mb-2">50K+</div>
            <div className="text-primary-200">Users Helped</div>
          </div>
          <div>
            <div className="text-4xl font-bold mb-2">1M+</div>
            <div className="text-primary-200">Jobs Analyzed</div>
          </div>
          <div>
            <div className="text-4xl font-bold mb-2">500K+</div>
            <div className="text-primary-200">Resumes Processed</div>
          </div>
          <div>
            <div className="text-4xl font-bold mb-2">95%</div>
            <div className="text-primary-200">User Satisfaction</div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to Transform Your Career?</h2>
        <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
          Join thousands of professionals who are already using AI Career Companion 
          to accelerate their career growth and enhance their learning journey.
        </p>
        <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-md font-medium">
          Get Started Today
        </button>
      </div>
    </div>
  );
};

export default About;
