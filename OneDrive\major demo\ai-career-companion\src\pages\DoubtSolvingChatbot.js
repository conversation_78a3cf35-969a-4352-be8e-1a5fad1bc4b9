import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>b, FaPaperPlane, <PERSON>a<PERSON><PERSON>, FaFilter } from 'react-icons/fa';

const DoubtSolvingChatbot = () => {
  const [messages, setMessages] = useState([
    { id: 1, type: 'bot', content: 'Hi! I\'m here to help solve your doubts. What would you like to learn about today?' }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('All');

  const subjects = ['All', 'Programming', 'Data Science', 'Web Development', 'Machine Learning', 'System Design'];

  const sendMessage = () => {
    if (!inputMessage.trim()) return;
    
    const userMessage = { id: Date.now(), type: 'user', content: inputMessage };
    setMessages(prev => [...prev, userMessage]);
    
    setTimeout(() => {
      const botResponse = { 
        id: Date.now() + 1, 
        type: 'bot', 
        content: 'Great question! Let me break this down for you step by step. Based on your query, here\'s what you need to know...' 
      };
      setMessages(prev => [...prev, botResponse]);
    }, 1000);
    
    setInputMessage('');
  };

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
            <FaLightbulb className="h-6 w-6 text-yellow-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Doubt Solving Chatbot</h1>
            <p className="text-gray-600">Get instant help with your learning questions</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaFilter className="mr-2" />
              Subject Filter
            </h3>
            <div className="space-y-2">
              {subjects.map((subject) => (
                <button
                  key={subject}
                  onClick={() => setSelectedSubject(subject)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                    selectedSubject === subject
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {subject}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-md h-96 flex flex-col">
            <div className="flex-1 p-6 overflow-y-auto">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`flex items-start space-x-2 max-w-xs lg:max-w-md ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${message.type === 'user' ? 'bg-primary-100' : 'bg-yellow-100'}`}>
                        {message.type === 'user' ? <FaUser className="h-4 w-4 text-primary-600" /> : <FaLightbulb className="h-4 w-4 text-yellow-600" />}
                      </div>
                      <div className={`px-4 py-2 rounded-lg ${message.type === 'user' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-900'}`}>
                        {message.content}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="border-t border-gray-200 p-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Ask your question..."
                />
                <button
                  onClick={sendMessage}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md"
                >
                  <FaPaperPlane className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoubtSolvingChatbot;
