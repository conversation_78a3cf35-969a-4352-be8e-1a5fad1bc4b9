import React from 'react';
import { Link } from 'react-router-dom';
import {
  FaShieldAlt, FaFileAlt, FaRoute, FaComments,
  FaCertificate, FaQuestionCircle, FaCalendarAlt,
  FaLightbulb, FaEye, FaTags, FaArrowRight,
  FaGraduationCap, FaBriefcase, FaCode, FaUsers, FaAward, FaRocket,
  FaChevronRight, FaPlay, FaStar, FaTrendingUp
} from 'react-icons/fa';

const Home = () => {
  const careerModules = [
    { path: '/job-scam-detector', name: 'Job Scam Detector', icon: FaShieldAlt, desc: 'Detect fraudulent job postings' },
    { path: '/resume-parser', name: 'Resume Parser', icon: FaFileAlt, desc: 'AI-powered resume analysis' },
    { path: '/career-path-recommender', name: 'Career Path Recommender', icon: FaRoute, desc: 'Find your ideal career path' },
    { path: '/interview-bot', name: 'Interview Q&A Bot', icon: FaComments, desc: 'Practice interview questions' },
    { path: '/offer-letter-verifier', name: 'Offer Letter Verifier', icon: FaCertificate, desc: 'Verify offer authenticity' },
  ];

  const learningModules = [
    { path: '/adaptive-quiz', name: 'Adaptive Quiz System', icon: FaQuestionCircle, desc: 'Personalized quizzes' },
    { path: '/study-planner', name: 'Study Planner', icon: FaCalendarAlt, desc: 'Plan your learning journey' },
    { path: '/doubt-solving-chatbot', name: 'Doubt Solving Bot', icon: FaLightbulb, desc: 'Get instant help' },
    { path: '/weak-topic-identifier', name: 'Weak Topic Identifier', icon: FaEye, desc: 'Identify learning gaps' },
    { path: '/topic-recommender', name: 'Topic Recommender', icon: FaTags, desc: 'Discover new topics' },
  ];

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 rounded-lg text-white p-8">
        <div className="max-w-4xl">
          <h1 className="text-4xl font-bold mb-4">
            AI-Powered Career & Learning Companion
          </h1>
          <p className="text-xl mb-6 text-primary-100">
            Empower your career journey with intelligent tools for job searching, 
            skill development, and continuous learning. Get personalized insights 
            and guidance powered by artificial intelligence.
          </p>
          <div className="flex space-x-4">
            <Link 
              to="/job-scam-detector" 
              className="bg-white text-primary-600 px-6 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors"
            >
              Get Started
            </Link>
            <Link 
              to="/about" 
              className="border border-white text-white px-6 py-3 rounded-md font-medium hover:bg-white hover:text-primary-600 transition-colors"
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>

      {/* Career & Job Assistant Modules */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Career & Job Assistant</h2>
          <span className="text-sm text-gray-500">9 modules available</span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {careerModules.map((module) => {
            const Icon = module.icon;
            return (
              <Link
                key={module.path}
                to={module.path}
                className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 group"
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <Icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600">
                      {module.name}
                    </h3>
                  </div>
                  <FaArrowRight className="h-4 w-4 text-gray-400 group-hover:text-primary-600" />
                </div>
                <p className="text-gray-600">{module.desc}</p>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Learning & Quiz Platform Modules */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Learning & Quiz Platform</h2>
          <span className="text-sm text-gray-500">5 modules available</span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {learningModules.map((module) => {
            const Icon = module.icon;
            return (
              <Link
                key={module.path}
                to={module.path}
                className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 group"
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <Icon className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-green-600">
                      {module.name}
                    </h3>
                  </div>
                  <FaArrowRight className="h-4 w-4 text-gray-400 group-hover:text-green-600" />
                </div>
                <p className="text-gray-600">{module.desc}</p>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white rounded-lg shadow-md p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Platform Statistics</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">15</div>
            <div className="text-gray-600">Total Modules</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">1,234</div>
            <div className="text-gray-600">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">5,678</div>
            <div className="text-gray-600">Jobs Analyzed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">9,012</div>
            <div className="text-gray-600">Quizzes Completed</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
