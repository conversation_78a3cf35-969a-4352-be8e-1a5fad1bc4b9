/* Professional GeeksforGeeks-style CSS for AI Career Companion */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  line-height: 1.6;
  color: #1e293b;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Enhanced Utility Classes */
.min-h-screen { min-height: 100vh; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-800 { background-color: #1f2937; }
.bg-white { background-color: white; }
.bg-primary-50 { background-color: #eff6ff; }
.bg-primary-100 { background-color: #dbeafe; }
.bg-primary-600 { background-color: #2563eb; }
.bg-primary-700 { background-color: #1d4ed8; }
.bg-green-100 { background-color: #dcfce7; }
.bg-green-600 { background-color: #16a34a; }
.bg-red-100 { background-color: #fee2e2; }
.bg-yellow-100 { background-color: #fef3c7; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-purple-100 { background-color: #f3e8ff; }
.bg-orange-100 { background-color: #fed7aa; }

.text-white { color: white; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-primary-600 { color: #2563eb; }
.text-primary-700 { color: #1d4ed8; }
.text-green-600 { color: #16a34a; }
.text-red-600 { color: #dc2626; }
.text-yellow-600 { color: #d97706; }
.text-blue-600 { color: #2563eb; }
.text-purple-600 { color: #9333ea; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-1 { flex: 1; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

.m-2 { margin: 0.5rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.ml-64 { margin-left: 16rem; }

.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

.border { border: 1px solid #d1d5db; }
.border-2 { border: 2px solid #d1d5db; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-primary-500 { border-color: #3b82f6; }

.w-2 { width: 0.5rem; }
.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.w-64 { width: 16rem; }
.w-full { width: 100%; }

.h-2 { height: 0.5rem; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-full { height: 100%; }

.max-w-lg { max-width: 32rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.leading-5 { line-height: 1.25rem; }

.hover\:bg-primary-700:hover { background-color: #1d4ed8; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:text-primary-600:hover { color: #2563eb; }
.hover\:text-gray-500:hover { color: #6b7280; }
.hover\:text-gray-900:hover { color: #111827; }
.hover\:text-white:hover { color: white; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

.focus\:outline-none:focus { outline: none; }
.focus\:ring-1:focus { box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5); }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:ring-primary-500:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:border-primary-500:focus { border-color: #3b82f6; }

.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }
.transition-shadow { transition: box-shadow 0.15s ease-in-out; }

.cursor-pointer { cursor: pointer; }
.disabled\:bg-gray-400:disabled { background-color: #9ca3af; }

.fixed { position: fixed; }
.relative { position: relative; }
.absolute { position: absolute; }
.top-0 { top: 0; }
.top-16 { top: 4rem; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.inset-y-0 { top: 0; bottom: 0; }
.z-50 { z-index: 50; }

.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-auto { overflow-x: auto; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

.block { display: block; }
.inline-block { display: inline-block; }
.inline-flex { display: inline-flex; }
.hidden { display: none; }

.uppercase { text-transform: uppercase; }
.tracking-wider { letter-spacing: 0.05em; }

.divide-y > * + * { border-top: 1px solid #e5e7eb; }
.divide-gray-200 > * + * { border-top-color: #e5e7eb; }

.pointer-events-none { pointer-events: none; }
.flex-shrink-0 { flex-shrink: 0; }

.placeholder-gray-400::placeholder { color: #9ca3af; }
.placeholder-gray-500::placeholder { color: #6b7280; }

.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }

.ring-1 { box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05); }
.ring-2 { box-shadow: 0 0 0 2px rgba(255, 255, 255, 1); }
.ring-white { box-shadow: 0 0 0 2px rgba(255, 255, 255, 1); }

/* Responsive Grid */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:col-span-2 { grid-column: span 2 / span 2; }
  .md\:block { display: block; }
  .md\:mt-0 { margin-top: 0; }
  .md\:flex-row { flex-direction: row; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:col-span-1 { grid-column: span 1 / span 1; }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:col-span-3 { grid-column: span 3 / span 3; }
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:max-w-md { max-width: 28rem; }
}

/* Custom styles for the app */
.sidebar {
  width: 16rem;
  height: calc(100vh - 4rem);
  position: fixed;
  left: 0;
  top: 4rem;
  background: white;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.navbar {
  height: 4rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.main-content {
  margin-left: 16rem;
  margin-top: 4rem;
  padding: 1.5rem;
  min-height: calc(100vh - 4rem);
}

/* Additional component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Animation classes */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Utility for text truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Status badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: #dcfce7;
  color: #166534;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-error {
  background-color: #fee2e2;
  color: #991b1b;
}

.badge-info {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Additional missing utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.justify-end { justify-content: flex-end; }
.justify-start { justify-content: flex-start; }

.self-center { align-self: center; }
.self-start { align-self: flex-start; }
.self-end { align-self: flex-end; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-5 > * + * { margin-top: 1.25rem; }

.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }

.pt-4 { padding-top: 1rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-8 { padding-top: 2rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-6 { padding-bottom: 1.5rem; }
.pb-8 { padding-bottom: 2rem; }
.pl-3 { padding-left: 0.75rem; }
.pl-4 { padding-left: 1rem; }
.pr-3 { padding-right: 0.75rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-8 { margin-top: 2rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-8 { margin-bottom: 2rem; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-4 { margin-right: 1rem; }

.w-5 { width: 1.25rem; }
.w-24 { width: 6rem; }
.w-32 { width: 8rem; }
.w-48 { width: 12rem; }
.w-96 { width: 24rem; }

.h-24 { height: 6rem; }
.h-32 { height: 8rem; }
.h-48 { height: 12rem; }
.h-64 { height: 16rem; }
.h-96 { height: 24rem; }

.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }

.border-l-4 { border-left: 4px solid; }
.border-r-2 { border-right: 2px solid; }
.border-t { border-top: 1px solid; }
.border-b { border-bottom: 1px solid; }

.border-red-400 { border-color: #f87171; }
.border-green-400 { border-color: #4ade80; }
.border-blue-400 { border-color: #60a5fa; }
.border-yellow-400 { border-color: #facc15; }
.border-purple-400 { border-color: #c084fc; }

.bg-red-500 { background-color: #ef4444; }
.bg-green-500 { background-color: #22c55e; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-yellow-500 { background-color: #eab308; }
.bg-purple-500 { background-color: #a855f7; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-400 { background-color: #9ca3af; }

.text-red-500 { color: #ef4444; }
.text-green-500 { color: #22c55e; }
.text-blue-500 { color: #3b82f6; }
.text-yellow-500 { color: #eab308; }
.text-purple-500 { color: #a855f7; }
.text-red-700 { color: #b91c1c; }
.text-green-700 { color: #15803d; }
.text-blue-700 { color: #1d4ed8; }
.text-yellow-700 { color: #a16207; }
.text-purple-700 { color: #7c3aed; }
.text-red-800 { color: #991b1b; }
.text-green-800 { color: #166534; }
.text-blue-800 { color: #1e40af; }
.text-yellow-800 { color: #854d0e; }
.text-purple-800 { color: #6b21a8; }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0em; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

.break-words { overflow-wrap: break-word; }
.break-all { word-break: break-all; }

/* Borders */
.border { border-width: 1px; }
.border-0 { border-width: 0; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-8 { border-width: 8px; }

.border-t { border-top-width: 1px; }
.border-r { border-right-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-l { border-left-width: 1px; }

.border-t-0 { border-top-width: 0; }
.border-r-0 { border-right-width: 0; }
.border-b-0 { border-bottom-width: 0; }
.border-l-0 { border-left-width: 0; }

.border-t-2 { border-top-width: 2px; }
.border-r-2 { border-right-width: 2px; }
.border-b-2 { border-bottom-width: 2px; }
.border-l-2 { border-left-width: 2px; }

.border-t-4 { border-top-width: 4px; }
.border-r-4 { border-right-width: 4px; }
.border-b-4 { border-bottom-width: 4px; }
.border-l-4 { border-left-width: 4px; }

.border-solid { border-style: solid; }
.border-dashed { border-style: dashed; }
.border-dotted { border-style: dotted; }
.border-double { border-style: double; }
.border-none { border-style: none; }

.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }

.rounded-t-none { border-top-left-radius: 0; border-top-right-radius: 0; }
.rounded-r-none { border-top-right-radius: 0; border-bottom-right-radius: 0; }
.rounded-b-none { border-bottom-right-radius: 0; border-bottom-left-radius: 0; }
.rounded-l-none { border-top-left-radius: 0; border-bottom-left-radius: 0; }

.rounded-t { border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem; }
.rounded-r { border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; }
.rounded-b { border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0.25rem; }
.rounded-l { border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; }

.rounded-t-md { border-top-left-radius: 0.375rem; border-top-right-radius: 0.375rem; }
.rounded-r-md { border-top-right-radius: 0.375rem; border-bottom-right-radius: 0.375rem; }
.rounded-b-md { border-bottom-right-radius: 0.375rem; border-bottom-left-radius: 0.375rem; }
.rounded-l-md { border-top-left-radius: 0.375rem; border-bottom-left-radius: 0.375rem; }

.rounded-t-lg { border-top-left-radius: 0.5rem; border-top-right-radius: 0.5rem; }
.rounded-r-lg { border-top-right-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }
.rounded-b-lg { border-bottom-right-radius: 0.5rem; border-bottom-left-radius: 0.5rem; }
.rounded-l-lg { border-top-left-radius: 0.5rem; border-bottom-left-radius: 0.5rem; }

.rounded-t-xl { border-top-left-radius: 0.75rem; border-top-right-radius: 0.75rem; }
.rounded-r-xl { border-top-right-radius: 0.75rem; border-bottom-right-radius: 0.75rem; }
.rounded-b-xl { border-bottom-right-radius: 0.75rem; border-bottom-left-radius: 0.75rem; }
.rounded-l-xl { border-top-left-radius: 0.75rem; border-bottom-left-radius: 0.75rem; }

.rounded-t-2xl { border-top-left-radius: 1rem; border-top-right-radius: 1rem; }
.rounded-r-2xl { border-top-right-radius: 1rem; border-bottom-right-radius: 1rem; }
.rounded-b-2xl { border-bottom-right-radius: 1rem; border-bottom-left-radius: 1rem; }
.rounded-l-2xl { border-top-left-radius: 1rem; border-bottom-left-radius: 1rem; }

.rounded-t-3xl { border-top-left-radius: 1.5rem; border-top-right-radius: 1.5rem; }
.rounded-r-3xl { border-top-right-radius: 1.5rem; border-bottom-right-radius: 1.5rem; }
.rounded-b-3xl { border-bottom-right-radius: 1.5rem; border-bottom-left-radius: 1.5rem; }
.rounded-l-3xl { border-top-left-radius: 1.5rem; border-bottom-left-radius: 1.5rem; }

.rounded-t-full { border-top-left-radius: 9999px; border-top-right-radius: 9999px; }
.rounded-r-full { border-top-right-radius: 9999px; border-bottom-right-radius: 9999px; }
.rounded-b-full { border-bottom-right-radius: 9999px; border-bottom-left-radius: 9999px; }
.rounded-l-full { border-top-left-radius: 9999px; border-bottom-left-radius: 9999px; }

/* Shadows */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06); }

/* Hover shadows */
.hover\:shadow-sm:hover { box-shadow: var(--shadow-sm); }
.hover\:shadow:hover { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.hover\:shadow-md:hover { box-shadow: var(--shadow-md); }
.hover\:shadow-lg:hover { box-shadow: var(--shadow-lg); }
.hover\:shadow-xl:hover { box-shadow: var(--shadow-xl); }
.hover\:shadow-2xl:hover { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-5 { opacity: 0.05; }
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-25 { opacity: 0.25; }
.opacity-30 { opacity: 0.3; }
.opacity-40 { opacity: 0.4; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-70 { opacity: 0.7; }
.opacity-75 { opacity: 0.75; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }
.opacity-95 { opacity: 0.95; }
.opacity-100 { opacity: 1; }

/* Positioning */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.inset-x-0 { right: 0; left: 0; }
.inset-y-0 { top: 0; bottom: 0; }

.top-0 { top: 0; }
.top-1 { top: 0.25rem; }
.top-2 { top: 0.5rem; }
.top-3 { top: 0.75rem; }
.top-4 { top: 1rem; }
.top-5 { top: 1.25rem; }
.top-6 { top: 1.5rem; }
.top-8 { top: 2rem; }
.top-10 { top: 2.5rem; }
.top-12 { top: 3rem; }
.top-16 { top: 4rem; }
.top-20 { top: 5rem; }
.top-24 { top: 6rem; }
.top-32 { top: 8rem; }
.top-40 { top: 10rem; }
.top-48 { top: 12rem; }
.top-56 { top: 14rem; }
.top-64 { top: 16rem; }

.right-0 { right: 0; }
.right-1 { right: 0.25rem; }
.right-2 { right: 0.5rem; }
.right-3 { right: 0.75rem; }
.right-4 { right: 1rem; }
.right-5 { right: 1.25rem; }
.right-6 { right: 1.5rem; }
.right-8 { right: 2rem; }
.right-10 { right: 2.5rem; }
.right-12 { right: 3rem; }
.right-16 { right: 4rem; }
.right-20 { right: 5rem; }
.right-24 { right: 6rem; }

.bottom-0 { bottom: 0; }
.bottom-1 { bottom: 0.25rem; }
.bottom-2 { bottom: 0.5rem; }
.bottom-3 { bottom: 0.75rem; }
.bottom-4 { bottom: 1rem; }
.bottom-5 { bottom: 1.25rem; }
.bottom-6 { bottom: 1.5rem; }
.bottom-8 { bottom: 2rem; }
.bottom-10 { bottom: 2.5rem; }
.bottom-12 { bottom: 3rem; }
.bottom-16 { bottom: 4rem; }
.bottom-20 { bottom: 5rem; }
.bottom-24 { bottom: 6rem; }

.left-0 { left: 0; }
.left-1 { left: 0.25rem; }
.left-2 { left: 0.5rem; }
.left-3 { left: 0.75rem; }
.left-4 { left: 1rem; }
.left-5 { left: 1.25rem; }
.left-6 { left: 1.5rem; }
.left-8 { left: 2rem; }
.left-10 { left: 2.5rem; }
.left-12 { left: 3rem; }
.left-16 { left: 4rem; }
.left-20 { left: 5rem; }
.left-24 { left: 6rem; }

/* Negative positioning */
.-top-1 { top: -0.25rem; }
.-top-2 { top: -0.5rem; }
.-top-3 { top: -0.75rem; }
.-top-4 { top: -1rem; }
.-top-5 { top: -1.25rem; }
.-top-6 { top: -1.5rem; }
.-top-8 { top: -2rem; }
.-top-10 { top: -2.5rem; }
.-top-12 { top: -3rem; }
.-top-16 { top: -4rem; }
.-top-20 { top: -5rem; }
.-top-24 { top: -6rem; }
.-top-32 { top: -8rem; }

.-right-1 { right: -0.25rem; }
.-right-2 { right: -0.5rem; }
.-right-3 { right: -0.75rem; }
.-right-4 { right: -1rem; }
.-right-5 { right: -1.25rem; }
.-right-6 { right: -1.5rem; }
.-right-8 { right: -2rem; }
.-right-10 { right: -2.5rem; }
.-right-12 { right: -3rem; }
.-right-16 { right: -4rem; }
.-right-20 { right: -5rem; }
.-right-24 { right: -6rem; }
.-right-32 { right: -8rem; }

.-bottom-1 { bottom: -0.25rem; }
.-bottom-2 { bottom: -0.5rem; }
.-bottom-3 { bottom: -0.75rem; }
.-bottom-4 { bottom: -1rem; }
.-bottom-5 { bottom: -1.25rem; }
.-bottom-6 { bottom: -1.5rem; }
.-bottom-8 { bottom: -2rem; }
.-bottom-10 { bottom: -2.5rem; }
.-bottom-12 { bottom: -3rem; }
.-bottom-16 { bottom: -4rem; }
.-bottom-20 { bottom: -5rem; }
.-bottom-24 { bottom: -6rem; }

.-left-1 { left: -0.25rem; }
.-left-2 { left: -0.5rem; }
.-left-3 { left: -0.75rem; }
.-left-4 { left: -1rem; }
.-left-5 { left: -1.25rem; }
.-left-6 { left: -1.5rem; }
.-left-8 { left: -2rem; }
.-left-10 { left: -2.5rem; }
.-left-12 { left: -3rem; }
.-left-16 { left: -4rem; }
.-left-20 { left: -5rem; }
.-left-24 { left: -6rem; }
.-left-32 { left: -8rem; }

/* Z-index */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

/* Transitions */
.transition-none { transition-property: none; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition { transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: background-color, border-color, color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.duration-700 { transition-duration: 700ms; }
.duration-1000 { transition-duration: 1000ms; }

.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* Transforms */
.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.transform-gpu { transform: translate3d(var(--tw-translate-x, 0), var(--tw-translate-y, 0), 0) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.transform-none { transform: none; }

.origin-center { transform-origin: center; }
.origin-top { transform-origin: top; }
.origin-top-right { transform-origin: top right; }
.origin-right { transform-origin: right; }
.origin-bottom-right { transform-origin: bottom right; }
.origin-bottom { transform-origin: bottom; }
.origin-bottom-left { transform-origin: bottom left; }
.origin-left { transform-origin: left; }
.origin-top-left { transform-origin: top left; }

.scale-0 { --tw-scale-x: 0; --tw-scale-y: 0; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-50 { --tw-scale-x: .5; --tw-scale-y: .5; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-75 { --tw-scale-x: .75; --tw-scale-y: .75; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-90 { --tw-scale-x: .9; --tw-scale-y: .9; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-95 { --tw-scale-x: .95; --tw-scale-y: .95; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-100 { --tw-scale-x: 1; --tw-scale-y: 1; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-125 { --tw-scale-x: 1.25; --tw-scale-y: 1.25; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-150 { --tw-scale-x: 1.5; --tw-scale-y: 1.5; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.hover\:scale-110:hover { --tw-scale-x: 1.1; --tw-scale-y: 1.1; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

.translate-x-0 { --tw-translate-x: 0px; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.translate-x-1 { --tw-translate-x: 0.25rem; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.translate-x-2 { --tw-translate-x: 0.5rem; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.translate-x-3 { --tw-translate-x: 0.75rem; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.translate-x-4 { --tw-translate-x: 1rem; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

.hover\:translate-x-1:hover { --tw-translate-x: 0.25rem; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.hover\:translate-x-2:hover { --tw-translate-x: 0.5rem; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

.rotate-0 { --tw-rotate: 0deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-1 { --tw-rotate: 1deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-2 { --tw-rotate: 2deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-3 { --tw-rotate: 3deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-6 { --tw-rotate: 6deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-12 { --tw-rotate: 12deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-45 { --tw-rotate: 45deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-90 { --tw-rotate: 90deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-180 { --tw-rotate: 180deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

.-rotate-180 { --tw-rotate: -180deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.-rotate-90 { --tw-rotate: -90deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.-rotate-45 { --tw-rotate: -45deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.-rotate-12 { --tw-rotate: -12deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.-rotate-6 { --tw-rotate: -6deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.-rotate-3 { --tw-rotate: -3deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.-rotate-2 { --tw-rotate: -2deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.-rotate-1 { --tw-rotate: -1deg; transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

.bg-red-50 { background-color: #fef2f2; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-yellow-50 { background-color: #fefce8; }
.bg-purple-50 { background-color: #faf5ff; }

.hover\:bg-red-700:hover { background-color: #b91c1c; }
.hover\:bg-green-700:hover { background-color: #15803d; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-yellow-700:hover { background-color: #a16207; }
.hover\:bg-purple-700:hover { background-color: #7c3aed; }

.hover\:bg-primary-50:hover { background-color: #eff6ff; }
.hover\:text-gray-700:hover { color: #374151; }

.whitespace-nowrap { white-space: nowrap; }
.line-through { text-decoration: line-through; }

.last\:border-b-0:last-child { border-bottom: 0; }

.group:hover .group-hover\:text-primary-600 { color: #2563eb; }
.group:hover .group-hover\:text-green-600 { color: #16a34a; }

.sticky { position: sticky; }
.top-4 { top: 1rem; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.bg-black { background-color: #000000; }
.bg-opacity-50 { background-color: rgba(0, 0, 0, 0.5); }

.mx-4 { margin-left: 1rem; margin-right: 1rem; }

.border-dashed { border-style: dashed; }

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Missing utility classes */
.h-16 { height: 4rem; }
.mx-8 { margin-left: 2rem; margin-right: 2rem; }
.pl-10 { padding-left: 2.5rem; }
.pr-3 { padding-right: 0.75rem; }
.focus\:placeholder-gray-400:focus::placeholder { color: #9ca3af; }
.focus\:ring-1:focus { box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5); }

/* Additional commonly used classes */
.rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }
.rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }
.rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }

.gap-1 { gap: 0.25rem; }
.gap-5 { gap: 1.25rem; }

.object-cover { object-fit: cover; }
.object-contain { object-fit: contain; }

.resize-none { resize: none; }
.resize { resize: both; }

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

.appearance-none { appearance: none; }

.outline-none { outline: none; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Additional missing classes for better layout */
.min-w-full { min-width: 100%; }
.divide-y > * + * { border-top-width: 1px; }
.divide-gray-100 > * + * { border-top-color: #f3f4f6; }

/* Table styles */
.min-w-full { min-width: 100%; }
.whitespace-nowrap { white-space: nowrap; }

/* Form styles */
.form-input,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="date"],
select,
textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  background-color: white;
}

.form-input:focus,
input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Button improvements */
button {
  cursor: pointer;
  border: none;
  font-family: inherit;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Link improvements */
a {
  text-decoration: none;
  color: inherit;
}

/* Sidebar improvements */
.sidebar {
  width: 16rem;
  height: calc(100vh - 4rem);
  position: fixed;
  left: 0;
  top: 4rem;
  background: white;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 40;
}

/* Navbar improvements */
.navbar {
  height: 4rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Main content improvements */
.main-content {
  margin-left: 16rem;
  margin-top: 4rem;
  padding: 1.5rem;
  min-height: calc(100vh - 4rem);
}

/* Responsive improvements */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .main-content {
    margin-left: 0;
  }

  .sidebar.open {
    transform: translateX(0);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
