import React, { useState } from 'react';
import { FaShieldAlt, FaExclamationTriangle, FaCheckCircle, FaTimesCircle, FaInfoCircle } from 'react-icons/fa';

const JobScamDetector = () => {
  const [jobDescription, setJobDescription] = useState('');
  const [analysis, setAnalysis] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleAnalyze = () => {
    if (!jobDescription.trim()) return;
    
    setIsAnalyzing(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockAnalysis = {
        scamScore: Math.floor(Math.random() * 100),
        riskLevel: Math.random() > 0.5 ? 'high' : Math.random() > 0.3 ? 'medium' : 'low',
        redFlags: [
          'Requests personal financial information upfront',
          'Promises unrealistic salary for minimal work',
          'Poor grammar and spelling in job posting',
          'Vague job description with no specific requirements'
        ],
        greenFlags: [
          'Company has verified website and contact information',
          'Realistic salary range for the position',
          'Clear job responsibilities and requirements'
        ],
        recommendation: 'Exercise caution when applying to this position. Consider researching the company further.'
      };
      setAnalysis(mockAnalysis);
      setIsAnalyzing(false);
    }, 2000);
  };

  const getRiskColor = (level) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const commonScamTypes = [
    {
      title: 'Advance Fee Scams',
      description: 'Requests money upfront for training, equipment, or background checks',
      warning: 'Legitimate employers never ask for money from job applicants'
    },
    {
      title: 'Identity Theft',
      description: 'Asks for SSN, bank details, or copies of ID before job offer',
      warning: 'Only provide sensitive information after receiving a formal job offer'
    },
    {
      title: 'Fake Check Scams',
      description: 'Sends fake checks for equipment purchases or overpayment',
      warning: 'Never deposit checks from unknown employers or return overpayments'
    },
    {
      title: 'Work-from-Home Scams',
      description: 'Promises easy money for simple tasks with no experience required',
      warning: 'Be skeptical of jobs that seem too good to be true'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
            <FaShieldAlt className="h-6 w-6 text-red-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Job Scam Detector</h1>
            <p className="text-gray-600">Analyze job postings for potential fraud and scam indicators</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Analysis Panel */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Job Description Analysis</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="jobDescription" className="block text-sm font-medium text-gray-700 mb-2">
                  Paste the job description here
                </label>
                <textarea
                  id="jobDescription"
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Copy and paste the complete job posting here, including company name, job title, description, requirements, and contact information..."
                />
              </div>
              <button
                onClick={handleAnalyze}
                disabled={!jobDescription.trim() || isAnalyzing}
                className="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
              >
                {isAnalyzing ? 'Analyzing...' : 'Analyze Job Posting'}
              </button>
            </div>
          </div>

          {/* Analysis Results */}
          {analysis && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Analysis Results</h2>
              
              {/* Scam Score */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Scam Risk Score</span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskColor(analysis.riskLevel)}`}>
                    {analysis.riskLevel.toUpperCase()} RISK
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full ${
                      analysis.scamScore > 70 ? 'bg-red-500' : 
                      analysis.scamScore > 40 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${analysis.scamScore}%` }}
                  ></div>
                </div>
                <div className="text-right text-sm text-gray-600 mt-1">{analysis.scamScore}/100</div>
              </div>

              {/* Red Flags */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-red-600 mb-3 flex items-center">
                  <FaTimesCircle className="mr-2" />
                  Red Flags Detected
                </h3>
                <ul className="space-y-2">
                  {analysis.redFlags.map((flag, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <FaExclamationTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{flag}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Green Flags */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-green-600 mb-3 flex items-center">
                  <FaCheckCircle className="mr-2" />
                  Positive Indicators
                </h3>
                <ul className="space-y-2">
                  {analysis.greenFlags.map((flag, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <FaCheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{flag}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Recommendation */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-2 flex items-center">
                  <FaInfoCircle className="mr-2" />
                  Recommendation
                </h3>
                <p className="text-blue-700">{analysis.recommendation}</p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Tips */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Safety Tips</h3>
            <ul className="space-y-3 text-sm text-gray-600">
              <li className="flex items-start space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></span>
                <span>Never pay money upfront for a job opportunity</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></span>
                <span>Research the company independently</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></span>
                <span>Be wary of jobs that seem too good to be true</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></span>
                <span>Verify contact information and company details</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></span>
                <span>Trust your instincts if something feels off</span>
              </li>
            </ul>
          </div>

          {/* Common Scam Types */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Common Scam Types</h3>
            <div className="space-y-4">
              {commonScamTypes.map((scam, index) => (
                <div key={index} className="border-l-4 border-yellow-400 pl-4">
                  <h4 className="font-semibold text-gray-900 text-sm">{scam.title}</h4>
                  <p className="text-xs text-gray-600 mb-1">{scam.description}</p>
                  <p className="text-xs text-yellow-700 font-medium">{scam.warning}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobScamDetector;
