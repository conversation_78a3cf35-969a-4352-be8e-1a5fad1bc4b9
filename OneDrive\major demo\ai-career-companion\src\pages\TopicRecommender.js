import React, { useState } from 'react';
import { FaTags, FaSearch, FaStar, FaClock, FaUsers } from 'react-icons/fa';

const TopicRecommender = () => {
  const [selectedRole, setSelectedRole] = useState('');
  const [recommendations, setRecommendations] = useState([]);

  const jobRoles = [
    'Software Engineer', 'Data Scientist', 'Product Manager', 'UX Designer',
    'DevOps Engineer', 'Machine Learning Engineer', 'Full Stack Developer'
  ];

  const topicData = {
    'Software Engineer': [
      { topic: 'System Design', difficulty: 'Advanced', duration: '4 weeks', popularity: 95, description: 'Learn to design scalable systems' },
      { topic: 'Algorithms & Data Structures', difficulty: 'Intermediate', duration: '6 weeks', popularity: 98, description: 'Master fundamental CS concepts' },
      { topic: 'Database Design', difficulty: 'Intermediate', duration: '3 weeks', popularity: 87, description: 'Design efficient database schemas' }
    ],
    'Data Scientist': [
      { topic: 'Machine Learning', difficulty: 'Advanced', duration: '8 weeks', popularity: 96, description: 'Build predictive models' },
      { topic: 'Statistics', difficulty: 'Intermediate', duration: '5 weeks', popularity: 89, description: 'Statistical analysis fundamentals' },
      { topic: 'Python for Data Science', difficulty: 'Beginner', duration: '4 weeks', popularity: 94, description: 'Python libraries for data analysis' }
    ]
  };

  const handleRoleSelect = (role) => {
    setSelectedRole(role);
    setRecommendations(topicData[role] || []);
  };

  const getDifficultyColor = (difficulty) => {
    const colors = {
      'Beginner': 'bg-green-100 text-green-800',
      'Intermediate': 'bg-yellow-100 text-yellow-800',
      'Advanced': 'bg-red-100 text-red-800'
    };
    return colors[difficulty];
  };

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <FaTags className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Topic Recommender</h1>
            <p className="text-gray-600">Discover relevant topics based on your target job role</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Select Your Target Role</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {jobRoles.map((role) => (
            <button
              key={role}
              onClick={() => handleRoleSelect(role)}
              className={`p-3 rounded-lg border-2 text-sm font-medium transition-colors ${
                selectedRole === role
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-200 hover:border-gray-300 text-gray-700'
              }`}
            >
              {role}
            </button>
          ))}
        </div>
      </div>

      {recommendations.length > 0 && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Recommended Topics for {selectedRole}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recommendations.map((topic, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">{topic.topic}</h3>
                    <div className="flex items-center space-x-1">
                      <FaStar className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm text-gray-600">{topic.popularity}%</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4">{topic.description}</p>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Difficulty</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(topic.difficulty)}`}>
                        {topic.difficulty}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 flex items-center">
                        <FaClock className="h-3 w-3 mr-1" />
                        Duration
                      </span>
                      <span className="text-sm font-medium text-gray-900">{topic.duration}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 flex items-center">
                        <FaUsers className="h-3 w-3 mr-1" />
                        Popularity
                      </span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-primary-600 h-2 rounded-full"
                            style={{ width: `${topic.popularity}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">{topic.popularity}%</span>
                      </div>
                    </div>
                  </div>
                  
                  <button className="w-full mt-4 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium text-sm">
                    Start Learning
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {!selectedRole && (
        <div className="bg-white rounded-lg shadow-md p-12 text-center">
          <FaSearch className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Choose Your Path</h3>
          <p className="text-gray-600">
            Select a job role above to get personalized topic recommendations 
            tailored to your career goals.
          </p>
        </div>
      )}
    </div>
  );
};

export default TopicRecommender;
