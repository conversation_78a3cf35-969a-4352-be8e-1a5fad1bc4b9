import React, { useState } from 'react';
import { FaFileAlt, FaUpload, FaUser, FaGraduationCap, FaBriefcase, FaCogs, FaStar, FaDownload } from 'react-icons/fa';

const ResumeParser = () => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [parsedData, setParsedData] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [gptFeedback, setGptFeedback] = useState(null);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      setUploadedFile(file);
      processResume(file);
    } else {
      alert('Please upload a PDF file');
    }
  };

  const processResume = (file) => {
    setIsProcessing(true);
    
    // Simulate processing
    setTimeout(() => {
      const mockParsedData = {
        personalInfo: {
          name: '<PERSON>',
          email: '<EMAIL>',
          phone: '+****************',
          location: 'San Francisco, CA',
          linkedin: 'linkedin.com/in/johnsmith'
        },
        summary: 'Experienced software engineer with 5+ years in full-stack development, specializing in React, Node.js, and cloud technologies.',
        experience: [
          {
            title: 'Senior Software Engineer',
            company: 'Tech Corp',
            duration: '2021 - Present',
            description: 'Led development of microservices architecture, improved system performance by 40%'
          },
          {
            title: 'Software Engineer',
            company: 'StartupXYZ',
            duration: '2019 - 2021',
            description: 'Developed React applications, collaborated with cross-functional teams'
          }
        ],
        education: [
          {
            degree: 'Bachelor of Science in Computer Science',
            institution: 'University of California',
            year: '2019',
            gpa: '3.8/4.0'
          }
        ],
        skills: [
          { name: 'JavaScript', level: 90 },
          { name: 'React', level: 85 },
          { name: 'Node.js', level: 80 },
          { name: 'Python', level: 75 },
          { name: 'AWS', level: 70 },
          { name: 'Docker', level: 65 }
        ],
        certifications: [
          'AWS Certified Solutions Architect',
          'Google Cloud Professional Developer'
        ]
      };

      const mockFeedback = {
        overallScore: 85,
        strengths: [
          'Strong technical skills clearly highlighted',
          'Quantified achievements with specific metrics',
          'Relevant work experience for target roles',
          'Professional formatting and structure'
        ],
        improvements: [
          'Add more specific project details',
          'Include leadership and soft skills',
          'Consider adding volunteer work or side projects',
          'Update contact information format'
        ],
        suggestions: [
          'Tailor resume for specific job applications',
          'Use action verbs to start bullet points',
          'Keep resume to 1-2 pages maximum',
          'Proofread for any grammatical errors'
        ]
      };

      setParsedData(mockParsedData);
      setGptFeedback(mockFeedback);
      setIsProcessing(false);
    }, 3000);
  };

  const getSkillColor = (level) => {
    if (level >= 80) return 'bg-green-500';
    if (level >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <FaFileAlt className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Resume Parser & GPT Feedback</h1>
            <p className="text-gray-600">Upload your resume for AI-powered analysis and improvement suggestions</p>
          </div>
        </div>
      </div>

      {/* Upload Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Upload Resume</h2>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          {!uploadedFile ? (
            <div>
              <FaUpload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg text-gray-600 mb-2">Upload your resume (PDF format)</p>
              <p className="text-sm text-gray-500 mb-4">Maximum file size: 10MB</p>
              <label className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md font-medium cursor-pointer inline-block">
                Choose File
                <input
                  type="file"
                  accept=".pdf"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          ) : (
            <div>
              <FaFileAlt className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-lg text-gray-900 mb-2">{uploadedFile.name}</p>
              <p className="text-sm text-gray-500 mb-4">
                {isProcessing ? 'Processing...' : 'Processing complete'}
              </p>
              {isProcessing && (
                <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                  <div className="bg-primary-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Parsed Data Display */}
      {parsedData && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Parsed Information */}
          <div className="space-y-6">
            {/* Personal Information */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaUser className="mr-2 text-blue-600" />
                Personal Information
              </h3>
              <div className="space-y-2">
                <p><span className="font-medium">Name:</span> {parsedData.personalInfo.name}</p>
                <p><span className="font-medium">Email:</span> {parsedData.personalInfo.email}</p>
                <p><span className="font-medium">Phone:</span> {parsedData.personalInfo.phone}</p>
                <p><span className="font-medium">Location:</span> {parsedData.personalInfo.location}</p>
                <p><span className="font-medium">LinkedIn:</span> {parsedData.personalInfo.linkedin}</p>
              </div>
            </div>

            {/* Experience */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaBriefcase className="mr-2 text-green-600" />
                Work Experience
              </h3>
              <div className="space-y-4">
                {parsedData.experience.map((exp, index) => (
                  <div key={index} className="border-l-4 border-green-400 pl-4">
                    <h4 className="font-semibold text-gray-900">{exp.title}</h4>
                    <p className="text-gray-600">{exp.company} • {exp.duration}</p>
                    <p className="text-sm text-gray-700 mt-1">{exp.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Education */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaGraduationCap className="mr-2 text-purple-600" />
                Education
              </h3>
              <div className="space-y-3">
                {parsedData.education.map((edu, index) => (
                  <div key={index}>
                    <h4 className="font-semibold text-gray-900">{edu.degree}</h4>
                    <p className="text-gray-600">{edu.institution} • {edu.year}</p>
                    <p className="text-sm text-gray-700">GPA: {edu.gpa}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills and Feedback */}
          <div className="space-y-6">
            {/* Skills */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaCogs className="mr-2 text-orange-600" />
                Skills Assessment
              </h3>
              <div className="space-y-3">
                {parsedData.skills.map((skill, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-700">{skill.name}</span>
                      <span className="text-sm text-gray-500">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${getSkillColor(skill.level)}`}
                        style={{ width: `${skill.level}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Certifications */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaStar className="mr-2 text-yellow-600" />
                Certifications
              </h3>
              <ul className="space-y-2">
                {parsedData.certifications.map((cert, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <FaStar className="h-4 w-4 text-yellow-500" />
                    <span className="text-gray-700">{cert}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* GPT Feedback */}
      {gptFeedback && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">AI-Powered Feedback</h2>
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-primary-600">{gptFeedback.overallScore}/100</span>
              <span className="text-sm text-gray-500">Overall Score</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Strengths */}
            <div>
              <h3 className="text-lg font-semibold text-green-600 mb-3">Strengths</h3>
              <ul className="space-y-2">
                {gptFeedback.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-sm text-gray-700">{strength}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Improvements */}
            <div>
              <h3 className="text-lg font-semibold text-yellow-600 mb-3">Areas for Improvement</h3>
              <ul className="space-y-2">
                {gptFeedback.improvements.map((improvement, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-sm text-gray-700">{improvement}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Suggestions */}
            <div>
              <h3 className="text-lg font-semibold text-blue-600 mb-3">Suggestions</h3>
              <ul className="space-y-2">
                {gptFeedback.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-sm text-gray-700">{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t border-gray-200">
            <button className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md font-medium flex items-center space-x-2">
              <FaDownload className="h-4 w-4" />
              <span>Download Improved Resume</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResumeParser;
