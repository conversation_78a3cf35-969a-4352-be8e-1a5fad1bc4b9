import React, { useState } from 'react';
import { FaCertificate, FaUpload, FaCheckCircle, FaExclamationTriangle, FaTimesCircle } from 'react-icons/fa';

const OfferLetterVerifier = () => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [verification, setVerification] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      setUploadedFile(file);
      processOfferLetter();
    }
  };

  const processOfferLetter = () => {
    setIsProcessing(true);
    setTimeout(() => {
      setVerification({
        isLegitimate: Math.random() > 0.3,
        confidenceScore: Math.floor(Math.random() * 30) + 70,
        redFlags: [
          'Unusual email domain for company communications',
          'Salary significantly above market rate',
          'Immediate start date requirement'
        ],
        verifiedElements: [
          'Company letterhead matches official branding',
          'Contact information is verifiable',
          'Job description aligns with company roles'
        ]
      });
      setIsProcessing(false);
    }, 2000);
  };

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <FaCertificate className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Offer Letter Verifier</h1>
            <p className="text-gray-600">Verify the authenticity of job offer letters</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Upload Offer Letter</h2>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          {!uploadedFile ? (
            <div>
              <FaUpload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg text-gray-600 mb-2">Upload your offer letter (PDF format)</p>
              <label className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md font-medium cursor-pointer inline-block">
                Choose File
                <input type="file" accept=".pdf" onChange={handleFileUpload} className="hidden" />
              </label>
            </div>
          ) : (
            <div>
              <FaCertificate className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-lg text-gray-900 mb-2">{uploadedFile.name}</p>
              {isProcessing && <div className="text-sm text-gray-500">Processing...</div>}
            </div>
          )}
        </div>
      </div>

      {verification && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Verification Results</h2>
          
          <div className="mb-6">
            <div className={`flex items-center space-x-3 p-4 rounded-lg ${
              verification.isLegitimate ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              {verification.isLegitimate ? (
                <FaCheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <FaTimesCircle className="h-8 w-8 text-red-600" />
              )}
              <div>
                <h3 className={`text-lg font-semibold ${verification.isLegitimate ? 'text-green-800' : 'text-red-800'}`}>
                  {verification.isLegitimate ? 'Offer Appears Legitimate' : 'Potential Issues Detected'}
                </h3>
                <p className={`text-sm ${verification.isLegitimate ? 'text-green-700' : 'text-red-700'}`}>
                  Confidence Score: {verification.confidenceScore}%
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-red-600 mb-3 flex items-center">
                <FaExclamationTriangle className="mr-2" />
                Red Flags
              </h3>
              <ul className="space-y-2">
                {verification.redFlags.map((flag, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-gray-700 text-sm">{flag}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-green-600 mb-3 flex items-center">
                <FaCheckCircle className="mr-2" />
                Verified Elements
              </h3>
              <ul className="space-y-2">
                {verification.verifiedElements.map((element, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-gray-700 text-sm">{element}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OfferLetterVerifier;
