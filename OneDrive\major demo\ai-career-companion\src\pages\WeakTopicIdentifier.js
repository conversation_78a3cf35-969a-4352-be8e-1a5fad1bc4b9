import React, { useState } from 'react';
import { FaEye, FaUpload, FaChartPie, FaLightbulb } from 'react-icons/fa';

const WeakTopicIdentifier = () => {
  const [analysisData, setAnalysisData] = useState({
    weakTopics: [
      { topic: 'Dynamic Programming', score: 45, improvement: 'Practice more complex DP problems' },
      { topic: 'System Design', score: 52, improvement: 'Study scalability patterns' },
      { topic: 'Graph Algorithms', score: 38, improvement: 'Focus on BFS/DFS variations' }
    ],
    strongTopics: [
      { topic: 'Arrays & Strings', score: 89 },
      { topic: 'Object-Oriented Programming', score: 85 },
      { topic: 'Database Design', score: 78 }
    ]
  });

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
            <FaEye className="h-6 w-6 text-red-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Weak Topic Identifier</h1>
            <p className="text-gray-600">Identify areas that need more focus in your learning</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Upload Quiz Results</h2>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <FaUpload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-lg text-gray-600 mb-2">Upload your quiz results or test scores</p>
          <button className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-md font-medium">
            Choose Files
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-red-600 mb-4 flex items-center">
            <FaChartPie className="mr-2" />
            Weak Topics
          </h2>
          <div className="space-y-4">
            {analysisData.weakTopics.map((topic, index) => (
              <div key={index} className="border-l-4 border-red-400 pl-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold text-gray-900">{topic.topic}</h3>
                  <span className="text-red-600 font-bold">{topic.score}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div
                    className="bg-red-500 h-2 rounded-full"
                    style={{ width: `${topic.score}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-600 flex items-start">
                  <FaLightbulb className="h-4 w-4 mr-2 mt-0.5 text-yellow-500" />
                  {topic.improvement}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-green-600 mb-4">Strong Topics</h2>
          <div className="space-y-4">
            {analysisData.strongTopics.map((topic, index) => (
              <div key={index} className="border-l-4 border-green-400 pl-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold text-gray-900">{topic.topic}</h3>
                  <span className="text-green-600 font-bold">{topic.score}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${topic.score}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeakTopicIdentifier;
