import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  FaHome, FaShieldAlt, FaFileAlt, FaRoute, FaComments,
  FaCertificate, FaQuestionCircle, FaCalendarAlt,
  FaLightbulb, FaEye, FaTags, FaUser, FaInfoCircle, FaEnvelope,
  FaBriefcase, FaGraduationCap, FaChevronRight
} from 'react-icons/fa';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    { path: '/', name: 'Dashboard', icon: FaHome, color: 'text-primary-600' },
    { path: '/about', name: 'About Platform', icon: FaInfoCircle, color: 'text-info-600' },
    { path: '/contact', name: 'Contact Support', icon: FaEnvelope, color: 'text-secondary-600' },

    // Career & Job Assistant
    {
      category: 'Career Development',
      icon: FaBriefcase,
      color: 'text-primary-600',
      items: [
        { path: '/job-scam-detector', name: 'Job Scam Detector', icon: FaShieldAlt, description: 'Detect fraudulent job postings' },
        { path: '/resume-parser', name: 'Resume Analyzer', icon: FaFileAlt, description: 'AI-powered resume feedback' },
        { path: '/career-path-recommender', name: 'Career Path Guide', icon: FaRoute, description: 'Personalized career recommendations' },
        { path: '/interview-bot', name: 'Interview Practice', icon: FaComments, description: 'Mock interview sessions' },
        { path: '/offer-letter-verifier', name: 'Offer Verification', icon: FaCertificate, description: 'Verify job offers' },
      ]
    },

    // Learning & Quiz Platform
    {
      category: 'Learning Hub',
      icon: FaGraduationCap,
      color: 'text-success-600',
      items: [
        { path: '/adaptive-quiz', name: 'Smart Quizzes', icon: FaQuestionCircle, description: 'Adaptive learning quizzes' },
        { path: '/study-planner', name: 'Study Planner', icon: FaCalendarAlt, description: 'Organize your learning' },
        { path: '/doubt-solving-chatbot', name: 'AI Tutor', icon: FaLightbulb, description: 'Get instant help' },
        { path: '/weak-topic-identifier', name: 'Weakness Analysis', icon: FaEye, description: 'Identify learning gaps' },
        { path: '/topic-recommender', name: 'Topic Suggestions', icon: FaTags, description: 'Discover new topics' },
        { path: '/profile', name: 'My Profile', icon: FaUser, description: 'Manage your account' },
      ]
    }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <div className="fixed left-0 top-16 h-full w-64 bg-white shadow-lg border-r border-gray-100 overflow-y-auto">
      <div className="p-4">
        {menuItems.map((section, index) => (
          <div key={index} className="mb-8">
            {section.category ? (
              <>
                <div className="flex items-center px-4 py-2 mb-4">
                  <section.icon className={`h-4 w-4 mr-2 ${section.color}`} />
                  <h3 className="text-xs font-bold text-gray-700 uppercase tracking-wider">
                    {section.category}
                  </h3>
                </div>
                <div className="space-y-1">
                  {section.items.map((item) => {
                    const Icon = item.icon;
                    return (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={`group flex items-start px-4 py-3 rounded-xl text-sm transition-all duration-200 ${
                          isActive(item.path)
                            ? 'bg-gradient-to-r from-primary-50 to-primary-100 text-primary-700 shadow-sm border-l-4 border-primary-500'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        }`}
                      >
                        <Icon className={`mr-3 h-4 w-4 mt-0.5 flex-shrink-0 ${
                          isActive(item.path) ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-600'
                        }`} />
                        <div className="flex-1 min-w-0">
                          <div className={`font-medium truncate ${isActive(item.path) ? 'text-primary-700' : 'text-gray-700'}`}>
                            {item.name}
                          </div>
                          <div className={`text-xs mt-0.5 ${isActive(item.path) ? 'text-primary-600' : 'text-gray-500'}`}>
                            {item.description}
                          </div>
                        </div>
                        {isActive(item.path) && <FaChevronRight className="ml-2 h-3 w-3 text-primary-500 mt-1" />}
                      </Link>
                    );
                  })}
                </div>
              </>
            ) : (
              <Link
                to={section.path}
                className={`group flex items-center px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                  isActive(section.path)
                    ? 'bg-gradient-to-r from-primary-50 to-primary-100 text-primary-700 shadow-sm border-l-4 border-primary-500'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <section.icon className={`mr-3 h-5 w-5 ${isActive(section.path) ? section.color : 'text-gray-400 group-hover:text-gray-600'}`} />
                <span className="font-medium">{section.name}</span>
                {isActive(section.path) && <FaChevronRight className="ml-auto h-3 w-3 text-primary-500" />}
              </Link>
            )}
          </div>
        ))}

        {/* Bottom section */}
        <div className="mt-8 pt-6 border-t border-gray-100">
          <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl p-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <FaGraduationCap className="h-4 w-4 text-white" />
              </div>
              <div>
                <div className="text-sm font-semibold text-primary-800">Pro Tips</div>
                <div className="text-xs text-primary-600">Get personalized insights</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
